# -*- coding: utf-8 -*-
import datetime
import time
import warnings
from typing import <PERSON>ple, List
from datetime import timed<PERSON>ta
import numpy as np

# for create_chrome_driver
from selenium.webdriver import Chrome
from selenium.webdriver.chrome.options import Options as Chrome_Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# for other functions
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, ElementClickInterceptedException, NoSuchElementException #, StaleElementReferenceException

from decorators import exception_handler, retry
from logger_setup import get_logger

warnings.filterwarnings("ignore")

# ----------------------------- Selector Configuration ----------------------------- #
# Centralize all selectors to make maintenance easier
SELECTORS = {
    "loading": ".loading.ivu-spin.ivu-spin-large.ivu-spin-fix",
    "venues_button": "venues",  # ID
    "date_selector_base": "#mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div.select_date_box.mobile > div.date_box > div",
    "table_body": "#scrollTable > table > tbody",
    "popup_close_button": "div.ivu-modal-wrap > div > div > div > div > div.ivu-modal-confirm-footer > button",
    "agree_checkbox": "ivu-checkbox-wrapper",  # CLASS_NAME
    "phone_input": "#mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div:nth-child(5) > div.box2.mobile > form > div:nth-child(1) > div > div > div > div > div > input",
    "submit_button": "#mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div.submit_order_box.mobile > div.action > div:nth-child(2)",
    "already_booked_popup": "body > div:nth-child(30) > div.ivu-modal-wrap > div > div > div > div > div.ivu-modal-confirm-footer > button",
    "pay_button": "#mobilePage > div > div > div.orderInfo-box.mobile > div.bottom-btn > div:nth-child(1)",
    "network_error_popup": "body > div:nth-child(25) > div.ivu-modal-wrap > div > div > div > div > div.ivu-modal-confirm-footer > button",
}

# ----------------------------- Helper functions ----------------------------- #
def wait_for_loading(driver: WebDriver, timeout: int = 10) -> None:
    WebDriverWait(driver, timeout).until(
        EC.invisibility_of_element_located(
            (By.CSS_SELECTOR, SELECTORS["loading"])
        )
    )


def is_time_within_range(time_range: str, start_time: str, end_time: str) -> bool:
    """
    example time_range: '2000-2200'
    """
    start, end = map(int, time_range.split("-"))
    return int(start_time) <= start and end <= int(end_time)


def split_time_ranges_in_preference(time_ranges: List[str]) -> List[str]:
    """
    Splits time ranges into 1-hour increments without duplicates.
    """
    expanded_ranges = []
    for time_range in time_ranges:
        start, end = map(int, time_range.split("-"))
        if end - start <= 100:
            continue
        for hour in range(start, end, 100):
            target = f"{hour:04d}-{hour + 100:04d}"
            if target not in expanded_ranges and target not in time_ranges:
                expanded_ranges.append(target)
    return expanded_ranges


# ------------------------------ Main functions ------------------------------ #
@exception_handler('获取预约时间段', [])
def get_constrained_preference(
    constraints: List[str], preference: List[str]
) -> List[Tuple[int, List[str]]]:
    """
    Get valid preference for different delta days.
    example return:
    [(2, ['2000-2200', '1900-2100']), (1, ['2000-2200']), (0, ['2000-2200'])]
    """
    logger = get_logger()
    # convert constraints to dict ({weekday: (start_time, end_time)})
    constraints_dict = {int(constraint[0]): tuple(constraint[2:].split('-')) for constraint in constraints}
    now = datetime.datetime.today()
    today = now.date()
    current_time = now.time()
    time_11_45 = datetime.time(11, 45, 0)
    time_12_00 = datetime.time(12, 0, 0)
    preference += split_time_ranges_in_preference(preference)
    constrained_preference = []

    for delta_day in (3, 2, 1, 0):
        if delta_day == 3 and current_time < time_11_45:
            continue

        # Python: Monday=0,...,Sunday=6
        python_weekday = (delta_day + today.weekday()) % 7
        weekday = python_weekday + 1
        constraint = constraints_dict.get(weekday)
        if constraint is None:
            constrained_preference.append((delta_day, preference))
        else:
            start_time, end_time = constraint
            if delta_day == 0:
                next_hour_dt = (datetime.datetime.combine(datetime.date.today(), current_time) + timedelta(hours=1))
                next_hour = next_hour_dt.strftime('%H00')
                if next_hour > start_time:
                    start_time = next_hour
            current_preference = [x for x in preference if is_time_within_range(x, start_time, end_time)]
            constrained_preference.append((delta_day, current_preference))
        date_obj = today + datetime.timedelta(days=delta_day)

        # 只能在中午11:50后预约未来3天的场馆
        if (delta_day == 3 and current_time < time_12_00):
            logger.info(f"{date_obj} 准备预约 √√")
            break
        else:
            logger.info(f"{date_obj} 可预约 √")

    if constrained_preference:
        logger.debug(f"constrained_preference: {constrained_preference}")
        return constrained_preference
    else:
        raise ValueError('No valid time slots.')


@exception_handler('启动chrome', None)
def create_chrome_driver(show_browser: bool, max_retries: int = 3) -> Chrome:
    """Create and return a Selenium WebDriver instance."""
    mobile_emulation = {
        "deviceName": "iPhone X",
    }
    chrome_options = Chrome_Options()
    if not show_browser:
        chrome_options.add_argument('headless')
    else:
        chrome_options.add_experimental_option('detach', True)
    chrome_options.add_argument('--ignore-certificate-errors')
    chrome_options.add_experimental_option("mobileEmulation", mobile_emulation)

    @retry(max_retries, '启动chrome', on_failure=time.sleep, on_failure_args=(1,))
    def create_driver():
        service = Service(ChromeDriverManager().install())
        driver = Chrome(service=service, options=chrome_options)
        # circumvent potential website detection of automation
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
            """
        })
        return driver

    return create_driver()


@exception_handler('门户登录', False)
@retry(3, '门户登录')
def login(driver: WebDriver, user_name: str, password: str) -> bool:
    """IAAA Login"""
    driver.get(
        "https://iaaa.pku.edu.cn/iaaa/oauth.jsp?appID=portal2017&appName=%E5%8C%97%E4%BA%AC%E5%A4%A7%E5%AD%A6%E6%A0%A1%E5%86%85%E4%BF%A1%E6%81%AF%E9%97%A8%E6%88%B7%E6%96%B0%E7%89%88&redirectUrl=https%3A%2F%2Fportal.pku.edu.cn%2Fportal2017%2FssoLogin.do"
    )

    wait_for_loading(driver)
    WebDriverWait(driver, 10).until(
        EC.visibility_of_element_located((By.ID, "logon_button"))
    )

    driver.find_element(By.ID, "user_name").send_keys(user_name)
    wait_for_loading(driver)
    driver.find_element(By.ID, "password").send_keys(password)
    wait_for_loading(driver)
    driver.find_element(By.ID, "logon_button").click()
    time.sleep(1)
    WebDriverWait(driver, 15).until(
        EC.visibility_of_element_located((By.ID, "venues"))
    )
    return True


@exception_handler('进入智慧场馆', False)
def go_to_smart_venue(driver: WebDriver) -> bool:
    """Navigate to the smart venue page."""
    wait_for_loading(driver)
    driver.find_element(By.ID, "venues").click()
    driver.switch_to.window(driver.window_handles[-1])
    time.sleep(1)
    wait_for_loading(driver)
    return True


@exception_handler('进入预约界面', False)
@retry(5, '进入预约界面')
def go_to_booking_page(driver: WebDriver, venue_type: str) -> bool:
    """Navigate to the venue booking page."""
    if driver.current_url.__contains__('venue-reservation'):
        return True
    WebDriverWait(driver, 10).until(
        EC.visibility_of_element_located(
            (By.XPATH, f"//dd[contains(text(), '{venue_type}')]")
        )
    ).click()
    time.sleep(1)
    wait_for_loading(driver)
    if driver.current_url.__contains__('venue-reservation'):
        return True
    raise RuntimeError('Failed to navigate to the booking page.')


@exception_handler('预约', [])
def book(
    driver: WebDriver,
    venue_type: str,
    venue_priority: List[int],
    constrained_preferences: List[Tuple[int, List[str]]],
    config: dict,
    refresh_second: float
) -> List[Tuple[datetime.date, int, str]]:
    """
    Book venues. Returns a list of booked venues with date, venue_num and time range.
    example input:
        venue_type = "羽毛球场"
        venue_priority = [4, 3, 9]
        constrained_preferences = [(2, ['2000-2200', '1900-2100']), (1, ['2000-2200']), (0, ['2000-2200'])]
    example return:
        [(datetime.date(2024, 6, 1), 3, '2000-2100'), (datetime.date(2024, 6, 1), 2, '2100-2200')]
    """

    def move_to_date(delta_day: int, max_retries: int = 50, timeout: int = 60) -> None:
        start_time = datetime.datetime.now()
        selector = f"{SELECTORS['date_selector_base']}[style*='order: {delta_day};']"
        # other possible selectors: deltaday 3: #mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div.select_date_box.mobile > div.date_box > div.active
        # deltaday 2: #mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div.select_date_box.mobile > div.date_box > div:nth-child(3)
        while not driver.current_url.__contains__('venue-reservation'):
            logger.warning(f'当前不在预约{venue_type}界面，尝试重新进入')
            go_to_booking_page(driver, venue_type)
            time.sleep(1)
        for attempt in range(1, max_retries + 1):
            if (datetime.datetime.now() - start_time).seconds > timeout:
                raise TimeoutException(f"Failed to move to delta_day {delta_day} after {attempt - 1} attempts in {timeout} seconds.")

            try:
                wait_for_loading(driver)
                WebDriverWait(driver, 0.5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
            except TimeoutException:
                logger.warning(f"{attempt}/{max_retries} Timeout: 未找到 delta_day {delta_day} 对应的元素，尝试刷新")
                if not driver.current_url.__contains__('venue-reservation'):
                    go_to_booking_page(driver, venue_type)
                else:
                    driver.refresh()
                time.sleep(0.2)

            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                class_attr = element.get_attribute('class')
                if class_attr is not None and 'forbidden' in class_attr:
                    logger.debug(f"Delta day {delta_day} element is forbidden. Retrying...")
                    continue

                WebDriverWait(driver, 2.5).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                element.click()
                logger.info(f"成功进入 delta_day {delta_day}.")
                return

            except ElementClickInterceptedException as e:
                error_message = str(e).split('\n')[0]
                logger.debug(f"{attempt}/{max_retries} 点击 delta_day {delta_day} 被打断: {error_message}")
                wait_for_loading(driver)
                close_popup_selector = SELECTORS["popup_close_button"]
                try:
                    WebDriverWait(driver, 0.4).until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, close_popup_selector))
                    ).click()
                    logger.info(f"成功关闭弹窗")
                except NoSuchElementException as e:
                    logger.warning(f"关闭弹窗的元素错误，请更新: {str(e)}")
                except Exception as e:
                    logger.warning(f"仍在加载中，或关闭弹窗时遇到错误: {str(e) if 'No symbol' not in str(e) else type(e).__name__}")
                    time.sleep(0.1)

            except TimeoutException:
                logger.warning(f"Timeout: delta_day {delta_day} 对应的元素不可点击，尝试刷新")
                if not driver.current_url.__contains__('venue-reservation'):
                    go_to_booking_page(driver, venue_type)
                else:
                    driver.refresh()
                time.sleep(1)
            except NoSuchElementException as e:
                logger.warning(f"仍在加载中，或关闭弹窗时遇到错误: {str(e) if 'No symbol' not in str(e) else type(e).__name__}")
            except Exception as e:
                logger.error(f"尝试进入 delta_day {delta_day} 时遇到未知错误: {str(e)}")

        # If maximum retries are reached
        raise RuntimeError(f"Failed to move to delta_day {delta_day} after {max_retries} attempts.")

    def get_venue_cells(driver: WebDriver, max_venue: int) -> np.ndarray:
        """
        Extracts the cells from the venue table and returns an array.
        """
        table_body_selector = SELECTORS["table_body"]
        WebDriverWait(driver, 10).until(
            lambda d: len(d.find_elements(By.CSS_SELECTOR, f"{table_body_selector} > tr")) >= 2
        )
        js_code = f"""
        let rows = document.querySelectorAll('{table_body_selector} tr');
        let data = [];
        for (let i = 0; i < rows.length - 1; i++) {{
            let cells = rows[i].querySelectorAll('td');
            let row_data = [];
            for (let j = 1; j < cells.length; j++) {{
                row_data.push(cells[j].textContent.includes('.'));
            }}
            data.push(row_data);
        }}
        return data;
        """

        venue_cells_data = driver.execute_script(js_code)
        venue_cells = np.array(venue_cells_data, dtype=bool)
        if venue_cells.shape[0] != max_venue:
            logger.warning(f"Expected {max_venue} rows, but found {venue_cells.shape[0]}.")
        logger.debug(f'venue_cells: shape {venue_cells.shape}\n{np.array(venue_cells, dtype=int)}')
        return venue_cells

    def column_to_time_range(column: int, open_time: str) -> str:
        """
        Derive the time range for a column.
        example return: '0800-0900'
        """
        if open_time == "06:50":
            if column == 1:
                return "0650-0800"
            open_time = "07:00"
        return f"{int(open_time[:2]) + (column - 1)}00-{int(open_time[:2]) + column}00"

    def time_range_to_columns(time_range: str, open_time: str) -> Tuple[int, int]:
        """
        Derive the column for a time range. Returns the starting and ending column.
        example return: (6, 7)
        """
        start_time, end_time = time_range.split("-")
        if open_time == "06:50":
            open_time = "07:00"
        if start_time == "0650":
            start_time = "0700"
        return (int(start_time[:2]) - int(open_time[:2]) + 1, int(end_time[:2]) - int(open_time[:2]))

    def get_venue_priority(venue_priority: List[int], venue_type: str, max_venue: int):
        if venue_type == "羽毛球场":
            default_priority = [3, 4, 9, 10, 2, 5, 8, 11, 1, 6, 7, 12]  # venues in the middle first
        else:
            default_priority = list(range(1, max_venue+1))  # min venue number first
        supplemented_priority = [x for x in default_priority if x not in venue_priority]
        return venue_priority + supplemented_priority

    logger = get_logger()
    venue_config = config.get(venue_type)
    if not venue_config:
        raise ValueError(f"No configuration found for venue: {venue_type}")

    open_time = venue_config["open_time"]
    max_venue = venue_config["max_venue"]
    booked_slots = []
    venue_priority = get_venue_priority(venue_priority, venue_type, max_venue)
    logger.debug(f"venue_priority: {venue_priority}")

    if constrained_preferences[0][0] == 3:
        now = datetime.datetime.now()
        refresh_sec, refresh_microsec = divmod(refresh_second, 1)
        refresh_sec = int(refresh_sec)
        refresh_microsec = round(refresh_microsec * 1e6)
        threshold_time = now.replace(hour=11, minute=45, second=0, microsecond=0)
        target_time = now.replace(hour=11, minute=59, second=refresh_sec, microsecond=refresh_microsec)

        if threshold_time < now < target_time:
            logger.info(f"当前 {now.time()}, 等待至 {target_time.time()}...")
            time.sleep(max((target_time - now).total_seconds(), 0))
            logger.info(f"当前 {datetime.datetime.now().time()}, 开始预约")

        driver.refresh()
        time.sleep(0.002)
        wait_for_loading(driver)

    for k in range(len(constrained_preferences)):
        delta_day, time_preferences = constrained_preferences[k]
        move_to_date(delta_day)
        logger.debug(f"time preferences: {time_preferences}")
        venue_cells = get_venue_cells(driver, max_venue)
        for time_preference in time_preferences:
            start_column, end_column = time_range_to_columns(time_preference, open_time)
            if venue_cells.shape[1] < end_column:
                continue

            available = venue_cells[:, start_column - 1:end_column].any(axis=0).all()
            if not available:
                logger.info(f"时间段 {time_preference} 不可预约 ×")
                continue

            # book the time slots
            logger.info(f"时间段 {time_preference} 预约中...")
            time_slots = tuple(range(start_column, end_column+1))
            for time_slot in time_slots:
                for venue_num in venue_priority:
                    if venue_num > venue_cells.shape[0]:
                        logger.warning(f"Venue {venue_num} is out of range ({venue_cells.shape[0]}).")
                        continue

                    is_bookable = venue_cells[venue_num - 1, time_slot - 1]
                    if not is_bookable:
                        continue
                    logger.info(f'预约{venue_num}号场 {column_to_time_range(time_slot, open_time)} 时间段中...')
                    selector = f'#scrollTable > table > tbody > tr:nth-child({venue_num}) > td:nth-child({time_slot + 1})'
                    element = driver.find_element(By.CSS_SELECTOR, selector)
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    WebDriverWait(driver, 1).until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                    element.click()
                    booked_slots.append((datetime.datetime.today().date() + timedelta(days=delta_day), venue_num, column_to_time_range(time_slot, open_time)))
                    logger.info(f"已预约{venue_num}号场 {column_to_time_range(time_slot, open_time)} 时间段")
                    break
                else:
                    continue # 内层循环没有 break,直接处理下一个 time_slot
                venue_priority.remove(venue_num)
                venue_priority.insert(0, venue_num)

            return booked_slots

    raise RuntimeError("No available time slots.")


@exception_handler('点击同意', False)
def click_agree(driver: WebDriver) -> bool:
    """Click the agree checkbox."""
    driver.switch_to.window(driver.window_handles[-1])
    wait_for_loading(driver)
    WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.CLASS_NAME, SELECTORS["agree_checkbox"]))
    ).click()
    wait_for_loading(driver)
    return True


@exception_handler('填写电话号码', False)
def fill_phone_number(driver: WebDriver, phone_number: str) -> bool:
    """Fill the phone number into the input field."""
    phone_number_selector = SELECTORS["phone_input"]
    input_field = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, phone_number_selector))
    )
    input_field.clear()
    input_field.send_keys(phone_number)
    wait_for_loading(driver)
    return True


@exception_handler('提交预约', False)
def click_book(driver: WebDriver) -> bool:
    driver.switch_to.window(driver.window_handles[-1])
    wait_for_loading(driver)
    selector = SELECTORS["submit_button"]
    WebDriverWait(driver, 10).until(EC.visibility_of_element_located((By.CSS_SELECTOR, selector))).click()
    return True

@exception_handler('检查是否已被预约', False)
def check_not_already_booked(driver: WebDriver) -> bool:
    """
    Check if the venue is already booked by someone else.
    Returns True if the venue is available, False if it's already booked.
    """
    already_booked_selector = SELECTORS["already_booked_popup"]
    try:
        WebDriverWait(driver, 4.5).until(EC.element_to_be_clickable((By.CSS_SELECTOR, already_booked_selector))).click()
        raise RuntimeError("场地已被其他人预约，弃疗")
    except TimeoutException:
        return True


@exception_handler('校园卡付款', (False, ""))
def click_pay(driver: WebDriver) -> Tuple[bool, str]:
    """Returns (True, "") if successful, (True, "error message") if click succeeded but other error occurred, and (False, "") if click failed"""
    driver.switch_to.window(driver.window_handles[-1])
    wait_for_loading(driver)
    time.sleep(1)
    selector = SELECTORS["pay_button"]
    # TODO: this selector is also present in the booking page, so it needs some checking process.
    WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector))).click()
    wait_for_loading(driver)

    network_error_selector = SELECTORS["network_error_popup"]
    try:
        WebDriverWait(driver, 3).until(EC.element_to_be_clickable((By.CSS_SELECTOR, network_error_selector))).click()
        get_logger().error("一卡通网络错误，请手动重试")
        return (True, "一卡通网络错误，请手动重试")
    except TimeoutException:
        return (True, "")
