# -*- coding: utf-8 -*-
import os
import re
import sys
import subprocess


def install_packages_from_requirements():
    # 动态获取 requirements.txt 路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    requirements_path = os.path.join(current_dir, "requirements.txt")
    
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", requirements_path],
            check=True
        )
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包时出错: {e}")
        sys.exit(1)

def env_check() -> list:
    # 动态生成 env_checked.txt 路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    env_checked_path = os.path.join(current_dir, "env_checked.txt")
    
    if not os.path.exists(env_checked_path):
        print('正在安装依赖包...')
        install_packages_from_requirements()
        with open(env_checked_path, "w", encoding='utf-8') as f:
            f.write("依赖包安装标记文件，删除此文件可触发重新安装。")
        print('依赖包安装完成')

    # Check for config files
    lst_conf = sorted([
        file_name for file_name in os.listdir()
        if re.match(r'^config[0-9]+\.ini$', file_name)
    ],
        key=lambda x: int(re.findall(r'[0-9]+', x)[0]))

    if not lst_conf:
        raise FileNotFoundError('未找到config0.ini')

    return lst_conf


if __name__ == '__main__':
    env_check()
