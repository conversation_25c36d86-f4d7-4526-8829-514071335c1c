import subprocess
import shutil
from pathlib import Path

PY_VERSIONS = {
    "3.12": r"C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe",
    "3.13": r"C:\Program Files\Python313\python.exe"
}

def main():
    for ver, py_exe in PY_VERSIONS.items():
        build_dir = Path(f"build-py{ver.replace('.', '')}")
        
        # 清理旧构建
        if build_dir.exists():
            shutil.rmtree(build_dir)
        
        # 执行构建
        cmd = [
            py_exe,
            "setup.py",
            "incremental",
            "--build-version",
            ver
        ]
        subprocess.run(cmd, check=True)

if __name__ == "__main__":
    main()
