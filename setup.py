from setuptools import setup, Extension, Command
from Cython.Build import cythonize
from pathlib import Path
import shutil
import sys
import subprocess
import argparse

# ---------------------------------------------------------------------------- #
# 解析参数
# ---------------------------------------------------------------------------- #
def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--build-version", help="Target Python version")
    return parser.parse_known_args()

args, unknown_args = parse_args()
sys.argv = [sys.argv[0]] + unknown_args  # 传递剩余参数给 setup

# ---------------------------------------------------------------------------- #
# 生成用户名哈希
# ---------------------------------------------------------------------------- #
def generate_hashes():
    """仅在授权用户列表变化时生成新哈希"""
    src = Path("authorized_users.txt")
    backup = Path("authorized_users.txt.bak")
    allowed_users_py = Path("scripts/allowed_users.py")

    # 判断是否需要生成新哈希
    needs_generate = False
    if src.exists():
        if not backup.exists() or src.read_bytes() != backup.read_bytes():
            needs_generate = True
        backup.write_bytes(src.read_bytes())  # 更高效的二进制复制

    if needs_generate or not allowed_users_py.exists():
        subprocess.run([sys.executable, "generate_user_hashes.py"], check=True)

generate_hashes()

# 排除文件和目录
EXCLUDE_FILES = {"test.py", "test_vpn.py", "cron.py", "run.py"}  # 新增需排除的文件
EXCLUDE_DIRS = {"original_pc"}              # 需排除的目录

# ---------------------------------------------------------------------------- #
# 文件移动和清理
# ---------------------------------------------------------------------------- #
def clean_and_move():
    build_suffix = f"-py{args.build_version.replace('.', '')}" if args.build_version else ""
    build_dir = Path(f"build{build_suffix}")
    build_dir.mkdir(exist_ok=True)

    libs_dir = build_dir / "libs"
    libs_dir.mkdir(exist_ok=True)
    
    scripts_dir = Path("scripts")

    # 移动加密脚本到 libs 目录
    for pyd_file in scripts_dir.rglob("*.pyd"):
        if pyd_file.name.startswith("__init__"):
            continue
        dest_path = libs_dir / pyd_file.name
        shutil.move(str(pyd_file), str(dest_path))

    # 移动资源（排除 original_pc）
    assets_src = Path("assets")
    assets_dst = build_dir / "assets"

    for item in assets_src.rglob("*"):
        if any(excl in str(item) for excl in EXCLUDE_DIRS):
            continue
        if item.is_file():
            # 计算相对路径（保留子目录结构）
            relative_path = item.relative_to(assets_src)
            target_path = assets_dst / relative_path

            # 创建父目录（如果不存在）
            target_path.parent.mkdir(parents=True, exist_ok=True)
            target_path.write_bytes(item.read_bytes())

    # 删除env_check.txt
    (libs_dir / "env_checked.txt").unlink(missing_ok=True)

    # 移动其他文件
    shutil.copy("scripts/requirements.txt", libs_dir / "requirements.txt")
    shutil.copy("scripts/run.py", libs_dir / "run.py")
    shutil.copy("LICENSE", build_dir / "LICENSE")
    shutil.copy("README.md", build_dir / "README.md")
    shutil.copy("config0.ini.sample", build_dir / "config0.ini.sample")
    shutil.copy("winRun.bat", build_dir / "winRun.bat")
    # shutil.copy("cli.py", build_dir / "cli.py")

    # 修改启动脚本
    bat_path = build_dir / "winRun.bat"
    with open(bat_path, "r", encoding="utf-8") as f:
        lines = f.readlines()
    for i, line in enumerate(lines):
        if line.strip().startswith("start /min python"):
            lines[i] = 'set PYTHONPATH=%~dp0libs && python libs/run.py\n'
            break
    with open(bat_path, "w", encoding="utf-8") as f:
        f.writelines(lines)
    
    # 保留 allowed_users.py 时间戳
    allowed_users_py = scripts_dir / "allowed_users.py"
    if allowed_users_py.exists():
        allowed_users_py.touch(exist_ok=True)

    # 清理原始位置的 .pyd 文件
    for pyd_file in scripts_dir.rglob("*.pyd"):
        pyd_file.unlink()
        
    # 清理残留目录
    for residual_dir in Path(".").glob(f"*win-amd64-cpython-{args.build_version.replace('.', '')}*"):
        if residual_dir.is_dir():
            shutil.rmtree(residual_dir, ignore_errors=True)
    
    if build_dir.exists():
        shutil.make_archive(
            base_name=str(build_dir),  # 压缩文件名（不含扩展名）
            format="zip",
            root_dir=build_dir.parent,
            base_dir=build_dir.name
        )

# ---------------------------------------------------------------------------- #
# Cython 扩展配置
# ---------------------------------------------------------------------------- #
extensions = []
for py_file in Path("scripts").rglob("*.py"):
    if py_file.name in EXCLUDE_FILES or any(excl in str(py_file) for excl in EXCLUDE_DIRS):
        continue
    
    module_name = str(py_file.with_suffix('')).replace("/", ".").replace("\\", ".")
    extensions.append(Extension(module_name, [str(py_file)]))

# ---------------------------------------------------------------------------- #
# 执行构建
# ---------------------------------------------------------------------------- #
class IncrementalBuildCommand(Command):
    description = "Incrementally update build directory"
    user_options = []

    def initialize_options(self):
        pass

    def finalize_options(self):
        pass

    def run(self):
        self.run_command("build_ext")  # 触发增量编译
        clean_and_move()              # 同步到 build 目录

# 执行构建
setup(
    name="pku_auto_book",
    ext_modules=cythonize(
        extensions,
        compiler_directives={"language_level": "3"},
        build_dir="build_temp"  # 避免与最终 build 目录冲突
    ),
    script_args=["build_ext", "--inplace"],
    cmdclass={
        "incremental": IncrementalBuildCommand,
    }
)

# 后处理：移动文件并清理
clean_and_move()
shutil.rmtree("build_temp")  # 删除临时构建目录
