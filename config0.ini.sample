; ---------------------------------------------------------------------------- ;

; 所有以“;”开头的行均为注释
; 布尔值选项(是/否)可填1/0或者True/False
; 请复制本文件为config0.ini，config1.ini，等等，再进行编辑

; ---------------------------------------------------------------------------- ;

[login]
; iaaa用户名与密码，以及手机号码
user_name=
password=
phone_number=

; ---------------------------------------------------------------------------- ;

[type]
; 预约场地的类型（需与预约界面显示完全一致）
; 例如：羽毛球场（也就是邱德拔），羽毛球馆（也就是五四），B1台球厅
venue_type= 羽毛球场

; 场地序号的优先级（数字间用英文逗号分隔，留空将使用默认顺序）
; 例如：venue_priority= 4,3,9 会优先预约4号场地，然后3号，9号，其余按系统默认排序
; 多个进程之间的 venue_priority 建议错开
venue_priority= 4,3,9,10

; ---------------------------------------------------------------------------- ;

[time]
; 时间段偏好（格式：开始时间-结束时间，最多两小时；多组用 "/" 分隔，按顺序尝试预约）
; 例如：2000-2200/1900-2100/2100-2300
; 如果都不满足，会尝试预约两小时中的一小时
preferences= 2000-2200

; 星期几特定约束（格式：星期几-开始时分-结束时分，多组用 "/" 分隔；留空表示无限制）
; 例如：7-2200-2300 会在预约周日场地时，只预约22:00-23:00的场地
constraints=

; 抢场地时，开始尝试刷新的秒数
; 例如：58.700表示在11:59:58.700开始刷新
refresh_second= 58.700

; ---------------------------------------------------------------------------- ;

[notice]
; 是否打开微信通知
wechat_notice= 1

; 方糖SCKey，请访问 https://sct.ftqq.com/ 获取（若不启用微信通知则可不填）
SCKEY=

; ---------------------------------------------------------------------------- ;

[captcha]
; 验证码识别需使用超级鹰，请访问 https://www.chaojiying.com/ 注册账号
; 赠送的1000积分短期够用，无需额外充值
cjy_user_name=
cjy_password=

; 注册完毕后，在 用户中心>>软件ID 生成软件ID，是6位左右的纯数字
cjy_soft_id=

; ---------------------------------------------------------------------------- ;

[options]
; 是否显示浏览器窗口（0：不显示；1：显示）
show_browser= 1

; 是否手动支付（0：自动支付；1：手动支付）
manual_pay= 0

; show_browser=1 时才有效
; 报错/预约成功时是否延迟关闭（0：直接关闭；1：延迟关闭）
; 延迟关闭时，只有在命令行中按Enter，浏览器才关闭
; 当然，直接按右上角的叉叉也可以直接关闭（多进程时，浏览器只能这样关闭）
delayed_exit_on_error= 1
delayed_exit_on_success= 1

; 每次运行前是否清空历史日志
clear_log= 1

; 每次运行前是否尝试关闭VPN
; 只支持LetsVPN；若有多个显示屏，需要VPN窗口打开时位于主显示屏
; 需要把“关闭快连”按钮的截图保存为“turnoffvpn.png”放在assets目录下
; 多进程时，建议确保至多一个配置文件启用此选项
manage_vpn= 0
