import subprocess
import time
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def check_vpn_status(process_name):
    """Check if LetsVPN is running"""
    logging.info(f"Checking if process '{process_name}' is running...")
    try:
        result = subprocess.run(f"tasklist | findstr /I {process_name}", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        if result.stdout:
            logging.info(f"Process '{process_name}' is running.")
            return True
        else:
            logging.info(f"Process '{process_name}' is not running.")
            return False
    except Exception as e:
        logging.error(f"Error checking VPN status: {e}")
        return False

def turn_off_vpn(process_name="LetsVPN.exe"):
    """Turn off LetsVPN"""
    logging.info(f"Turning off process '{process_name}'...")
    try:
        subprocess.run(f"taskkill /F /IM {process_name}", shell=True, check=True)
        logging.info(f"Process '{process_name}' terminated successfully.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Failed to terminate process '{process_name}': {e}")
    except Exception as e:
        logging.error(f"Error while turning off VPN: {e}")

def turn_on_vpn(executable_path="C:\\Path\\To\\LetsVPN.exe"):
    """Turn on LetsVPN"""
    logging.info(f"Starting LetsVPN from '{executable_path}'...")
    try:
        subprocess.Popen(executable_path, shell=True)
        logging.info("VPN started successfully.")
    except Exception as e:
        logging.error(f"Error starting LetsVPN: {e}")

def create_chrome_session():
    """Create Chrome session (this part is omitted in your code)"""
    logging.info("Creating Chrome session...")
    # Your Chrome setup code here
    pass

def main():
    logging.info("Starting the VPN control process...")
    
    # Step 1: Check if VPN is on
    process_name = "LetsPRO.exe"  # Replace with the exact process name you found in Task Manager
    was_vpn_on = check_vpn_status(process_name=process_name)

    # Step 2: Turn off the VPN if it was on
    if was_vpn_on:
        turn_off_vpn(process_name=process_name)
        time.sleep(2)  # Give it a moment to turn off

    # Step 3: Proceed with your Chrome setup (this part is omitted in your code)
    create_chrome_session()

    # Step 4: Turn the VPN back on if it was originally on
    if was_vpn_on:
        # Replace the path below with the actual path to LetsVPN.exe
        # TODO modify this
        executable_path = "C:\\Path\\To\\LetsVPN.exe"
        turn_on_vpn(executable_path=executable_path)

    logging.info("VPN control process completed.")

if __name__ == "__main__":
    main()
