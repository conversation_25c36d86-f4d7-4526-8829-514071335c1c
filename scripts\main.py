# -*- coding: utf-8 -*-
import sys
import os
import time
from env_check import env_check
config_files = env_check()

from functools import partial
import warnings
import multiprocessing as mp
from typing import List, Tuple, Callable
from configparser import ConfigParser
import logging
import csv
import hashlib

from allowed_users import SALT, ALLOWED_HASHES
from page_func import (
    exception_handler,
    get_constrained_preference,
    create_chrome_driver,
    login,
    go_to_smart_venue,
    go_to_booking_page,
    book,
    click_agree,
    fill_phone_number,
    click_book,
    check_not_already_booked,
    click_pay,
)
from vpn_control import create_manage_vpn_state_decorator
from notice import wechat_notification
from captcha import get_image, click_characters_in_image
import logger_setup

warnings.filterwarnings('ignore')

TEST = 0

def setup_logging(config_file: str) -> logging.Logger:
    log_file = f'{config_file.split(".")[0]}.log'
    
    # Create handlers
    fh = logging.FileHandler(log_file, mode='a', encoding='utf-8')
    fh.setLevel(logging.DEBUG)
    
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    
    # Create formatter and add it to handlers
    formatter = logging.Formatter(
        '%(asctime)s.%(msecs)03d [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)

    # Get the root logger and set level
    logger = logging.getLogger(config_file)
    logger.setLevel(logging.DEBUG)
    logger.handlers = []  # Clear existing handlers
    logger.addHandler(fh)
    logger.addHandler(ch)

    # Set external library log levels to WARNING
    for lib in ['selenium', 'WDM', 'urllib3', 'PIL']:
        logging.getLogger(lib).setLevel(logging.WARNING)

    return logger

def load_config(config: str) -> Tuple[str, str, str, str, List[int], List[str], List[str], float, bool, str, str, str, str, bool, bool, bool, bool, bool, bool]:
    '''Load configuration from a given file.'''
    def is_user_authorized(user_name: str) -> bool:
        hasher = hashlib.sha256()
        hasher.update(SALT)
        hasher.update(user_name.encode('utf-8'))
        input_hash = hasher.hexdigest()
        return input_hash in ALLOWED_HASHES

    conf = ConfigParser()
    conf.read(config, encoding='utf8')

    user_name = conf['login']['user_name']
    assert user_name, '用户名不能为空'
    assert is_user_authorized(user_name), '用户未授权'
    password = conf['login']['password']
    phone_number = conf['login']['phone_number']
    venue_type = conf['type']['venue_type']
    venue_priority = conf['type']['venue_priority']
    venue_priority = [int(x) for x in venue_priority.split(',') if x.strip()]
    preferences = conf['time']['preferences'].split('/')
    constraints = conf['time']['constraints'].split('/')
    constraints.remove('')
    refresh_second = conf.getfloat('time', 'refresh_second')
    wechat_notice = conf.getboolean('notice', 'wechat_notice')
    sckey = conf['notice']['SCKEY']
    cjy_user_name = conf['captcha']['cjy_user_name']
    cjy_password = conf['captcha']['cjy_password']
    cjy_soft_id = conf['captcha']['cjy_soft_id']
    show_browser = conf.getboolean('options', 'show_browser')
    manual_pay = conf.getboolean('options', 'manual_pay')
    delayed_exit_on_error = conf.getboolean('options', 'delayed_exit_on_error')
    delayed_exit_on_success = conf.getboolean('options', 'delayed_exit_on_success')
    clear_log = conf.getboolean('options', 'clear_log')
    manage_vpn = conf.getboolean('options', 'manage_vpn')
    
    logger = logger_setup.setup_logger(config)
    logger.info(f"准备预约: {venue_type}")

    return (
        user_name, password, phone_number, venue_type, venue_priority, preferences, constraints, refresh_second,
        wechat_notice, sckey, cjy_user_name, cjy_password, cjy_soft_id,
        show_browser, manual_pay, delayed_exit_on_error,
        delayed_exit_on_success, clear_log, manage_vpn
    )

@exception_handler('加载场馆配置', {})
def load_venue_config(file_path: str = 'assets/venue_info.csv') -> dict:
    venue_config = {}
    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            name = row['name']
            max_venue = int(row['max_venue'])
            venue_config[name] = {
                'open_time': row['open_time'],
                'max_venue': max_venue,
            }

    return venue_config

def log_process_status(clear_log: bool) -> None:
    '''Log the start and end of the process.'''
    logger = logger_setup.get_logger()
    log_file = logger.handlers[0].baseFilename # type: ignore
    if clear_log:
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write('')
    log_file_name = os.path.basename(log_file)
    logger.info(f'=============== Process started for {log_file_name} ===============')

def end_process_status(status: bool) -> None:
    '''Log the end status of the process.'''
    logger = logger_setup.get_logger()
    log_file = logger.handlers[0].baseFilename # type: ignore
    logger.info(f'日志记录于 {log_file}')
    logger.info(f'=============== Process ended with status {status} ===============' + '\n\n\n')

def manage_vpn_state(manage_vpn: bool) -> Callable:
    '''Manage VPN state if required.'''
    if manage_vpn:
        vpn_decorator = create_manage_vpn_state_decorator(manage_vpn_on=True)
        return vpn_decorator
    else:
        def noop_decorator(*args, **kwargs):
            def decorator(func):
                return func
            return decorator
        return noop_decorator

def page(config_file: str, is_multiprocess = False) -> bool:
    '''Complete the booking process for a given config.'''
    (
        user_name, password, phone_number, venue_type, venue_priority, preferences, constraints, refresh_second,
        wechat_notice, sckey, cjy_user_name, cjy_password, cjy_soft_id,
        show_browser, manual_pay, delayed_exit_on_error,
        delayed_exit_on_success, clear_log, manage_vpn
    ) = load_config(config_file)
    
    log_process_status(clear_log)
    vpn_decorator = manage_vpn_state(manage_vpn)
    
    @exception_handler('', False)
    def booking_process() -> bool:
        venue_config = load_venue_config()

        constrained_preferences = get_constrained_preference(constraints, preferences)
        if not constrained_preferences:
            return False

        driver = create_chrome_driver(show_browser)

        def delayed_quit(return_value, force_delayed_exit=False) -> bool:
            nonlocal driver
            delayed_exit = show_browser and (delayed_exit_on_success if return_value else delayed_exit_on_error)
            logger = logger_setup.get_logger()
            if delayed_exit or force_delayed_exit:
                if not is_multiprocess:
                    try:
                        logger.info('等待手动关闭浏览器中...')
                        input('按Enter键关闭浏览器')
                    except EOFError:
                        logger.warning('在跑多进程，不关闭浏览器')
                        return return_value
                else:
                    logger.info('在跑多进程，不关闭浏览器')
                    return return_value
            try:
                driver.quit()
            except Exception as e:
                logger.warning(f"Attempt to quit driver failed: {str(e)}")
            return return_value

        if not driver:
            return delayed_quit(False)

        if not (login(driver, user_name, password) and
                go_to_smart_venue(driver) and
                go_to_booking_page(driver, venue_type)):
            return delayed_quit(False)

        booked_venues = book(driver, venue_type, venue_priority, constrained_preferences, venue_config, refresh_second)
        if not booked_venues:
            return delayed_quit(False)

        book_success = click_agree(driver) and fill_phone_number(driver, phone_number) and click_book(driver)
        if not book_success:
            return delayed_quit(False)
        
        if not click_characters_in_image(driver, *get_image(driver), cjy_user_name, cjy_password, cjy_soft_id):
            return delayed_quit(False)
        
        if not check_not_already_booked(driver):
            return delayed_quit(False)

        if not manual_pay:
            pay_success, error_message = click_pay(driver)
            if pay_success and wechat_notice:
                wechat_notification(user_name, venue_type, booked_venues, sckey, error_message)
            else:
                return delayed_quit(False)
        else:
            logger_setup.get_logger().info('请手动完成支付')
            wechat_notification(user_name, venue_type, booked_venues, sckey, '请手动完成支付')
            return delayed_quit(True, True)

        return delayed_quit(True)

    booking = vpn_decorator('LetsVPN', 'assets/turnoffvpn.png')(booking_process)
    status = booking()
    end_process_status(status)

    return status

def main() -> List[bool]:
    global config_files
    if len(config_files) == 1:
        return [page(config_files[0])]
    print(f'并行预约{len(config_files)}个场馆：{config_files}')
    with mp.Pool() as pool:
        task_func = partial(page, is_multiprocess=True)
        results = pool.map(task_func, config_files)
    return results

if __name__ == '__main__':
    if TEST:
        page('config0.ini')
    else:
        main()
