# -*- coding: utf-8 -*-
import re

sensitive_keys = {
    'login': ['user_name', 'password', 'phone_number'],
    'notice': ['SCKEY'],
    'captcha': ['cjy_user_name', 'cjy_password', 'cjy_soft_id']
}

options_mapping = {
    'show_browser': '1',
    'manual_pay': '0',
    'delayed_exit_on_error': '1',
    'delayed_exit_on_success': '1',
    'clear_log': '1',
    'manage_vpn': '0'
}

def generate_sample_config(input_file, output_file):
    current_section = None
    with open(input_file, 'r', encoding='utf-8') as infile, open(output_file, 'w', encoding='utf-8') as outfile:
        for line in infile:
            stripped_line = line.strip()
            # 处理注释或空行
            if stripped_line.startswith(';') or not stripped_line:
                outfile.write(line)
                continue
            # 处理节标题
            if stripped_line.startswith('[') and stripped_line.endswith(']'):
                current_section = stripped_line[1:-1].strip().lower()
                outfile.write(line)
                continue
            # 处理键值对
            if '=' in line:
                key_part, value_part = line.split('=', 1)
                key = key_part.strip()
                # 处理敏感信息
                if current_section in sensitive_keys and key in sensitive_keys[current_section]:
                    new_line = f"{key}=" + '\n'
                elif current_section == 'options' and key in options_mapping:
                    # 保留原始格式并替换值
                    match = re.match(r'^(\s*)(.*?)(\s*)$', value_part)
                    if match:
                        leading, _, trailing = match.groups()
                        new_value = options_mapping[key]
                        new_line = f"{key_part}={leading}{new_value}{trailing}"
                    else:
                        new_line = line
                else:
                    new_line = line
                outfile.write(new_line)
            else:
                outfile.write(line)

# 使用示例
generate_sample_config('config0.ini', 'config0.ini.sample')
