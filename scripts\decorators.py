# -*- coding: utf-8 -*-
from typing import Callable
from logger_setup import get_logger

def exception_handler(log_message: str = '', default_return=None):
    """
    Decorator to handle exceptions for a function, log a custom message, and return a default value.

    :param log_message: Custom message to log in case of an exception.
    :param default_return: Value to return if the function raises an exception.
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger()
            try:
                if log_message:
                    logger.info(f"{log_message}中...")
                return_value = func(*args, **kwargs)
                if log_message:
                    logger.info(f"{log_message}成功")
                return return_value
            except Exception as e:
                logger.exception(f"丸辣！{log_message}失败: {e}")
                return default_return
        return wrapper
    return decorator


def retry(max_retries: int, log_message: str, on_failure: None | Callable=None, on_failure_args: tuple=()):
    """
    A decorator to retry a function if it raises an exception.
    :param max_retries: The maximum number of retries before giving up.
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger()
            for attempt in range(1, max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.warning(f'{log_message}失败×{attempt}: {e}')
                    if attempt < max_retries:
                        if on_failure:
                            on_failure(*on_failure_args)
                        logger.info(f'重试中...')
                    else:
                        raise RuntimeError(f'{log_message} failed after {max_retries} attempts') from e
        return wrapper
    return decorator
