import hashlib
import os

# 预定义盐（可自定义修改）
SALT = b"pku_auto_book_secure_salt_2024"

def generate_hashes():
    with open("authorized_users.txt", "r") as f:
        users = [line.strip() for line in f if line.strip()]

    hashes = []
    for user in users:
        # 加盐哈希（SHA-256）
        hasher = hashlib.sha256()
        hasher.update(SALT)
        hasher.update(user.encode('utf-8'))
        hashes.append(hasher.hexdigest())

    # 生成 Python 文件（供主程序调用）
    with open("scripts/allowed_users.py", "w") as f:
        f.write(f"# Auto-generated hashes\n")
        f.write(f"SALT = {SALT}\n")
        f.write("ALLOWED_HASHES = [\n")
        for h in hashes:
            f.write(f'    "{h}",\n')
        f.write("]\n")

if __name__ == "__main__":
    generate_hashes()
