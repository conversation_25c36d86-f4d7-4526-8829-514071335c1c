# pku_auto_book

## 功能

- 实现PKU智慧场馆自动预约
- 若在11:45-12:00之间运行，则在12:00抢三天后的场馆（不会管其他日期）
- 若在其他时间运行，则尝试预约近三天所有符合要求的时间段
- 预约成功时，会用校园卡付款，并微信推送（余额不足会导致预约失败）
- 部分代码和README引自[大佬的项目](https://github.com/lyqqqqqqqqq/PKUAutoBookingVenues)
- 暂时只支持windows平台


## 准备工作

### 安装 Python

- 需要3.12以上版本的python，可从[Python 官网](https://www.python.org/downloads/)下载安装
- 执行程序时会自动安装相关的包


## 用法

1. 将 `config0.ini.sample` 复制为 `config0.ini`，并根据文件内的注释修改 `config0.ini` 中的条目
2. 双击 `winRun.bat` （相当于用python运行 `main.py`）
3. 继续设置 `config1.ini` 等，可同时进行多个预约
4. 第一次运行可能会出现[弹窗](assets/第一次运行的弹窗.png)，勾选“不再提示”并关闭弹窗后重启程序即可


## 定时运行

- 用 `winRun.bat` 配合 Windows 任务计划管理，实现自动预约，参考[教程](assets/Win10下定时启动程序或脚本.htm)
- 请确保计划开始执行任务的时间点电脑处于开机且未休眠的状态。
- 除了按教程设置外，建议将该计划任务的设置中最后一项改为“选择并行运行新实例”，参考[预防问题](assets/预防计划任务报错（0x800710E0）.htm)


## 责任须知

- 本项目仅供参考学习，造成的一切后果由使用者自行承担

## 证书

[Apache License 2.0](https://github.com/asHOH/pku_auto_book/blob/main/LICENSE)
