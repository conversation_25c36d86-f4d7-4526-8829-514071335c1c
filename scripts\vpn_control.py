# -*- coding: utf-8 -*-
import pyautogui
import time
import logging
import pygetwindow as gw
from pyautogui import ImageNotFoundException
from decorators import exception_handler

logger = logging.getLogger(__name__)

@exception_handler('激活VPN窗口', False)
def evoke_window(window_title="LetsVPN") -> bool:
    windows = gw.getWindowsWithTitle(window_title)
    if windows:
        window = windows[0]
        window.restore()
        window.activate()
        time.sleep(0.2)
        return True
    else:
        logger.info(f"未找到{window_title}窗口")
        return False


@exception_handler('最小化VPN窗口')
def minimize_window(window_title="LetsVPN") -> None:
    windows = gw.getWindowsWithTitle(window_title)
    if windows:
        window = windows[0]
        window.minimize()
        logger.debug(f"Successfully minimized '{window_title}'.")
    else:
        logger.error(f"No window found with title '{window_title}'. Make sure the application is running.")

@exception_handler('关闭VPN')
def turn_off_vpn(image_path, confidence_level=0.8):
    try:
        button_location = pyautogui.locateOnScreen(image_path, confidence=confidence_level)
        button_center = pyautogui.center(button_location)
        pyautogui.click(button_center)
        time.sleep(0.2)
        return button_center
    except ImageNotFoundException:
        logger.warning("Could not find the turn-off button on the screen.")
        return None


@exception_handler('打开VPN')
def turn_on_vpn(button_position):
    if button_position:
        pyautogui.click(button_position)
        time.sleep(0.2)
        logger.info("Successfully turned VPN on.")
    else:
        logger.error("Button position for turning on VPN is not available.")


@exception_handler('检查VPN状态', False)
def is_vpn_on(image_path, confidence_level=0.8):
    try:
        pyautogui.locateOnScreen(image_path, confidence=confidence_level)
        logger.info(f"VPN is ON.")
        return True
    except ImageNotFoundException:
        logger.info(f"VPN is OFF.")
        return False

def create_manage_vpn_state_decorator(manage_vpn_on):
    def manage_vpn_state(vpn_title: str, button_image_path: str):
        def decorator(func):
            if not manage_vpn_on:
                return func  # If manage_vpn is False, return a no-op decorator

            def wrapper(*args, **kwargs):
                vpn_control_on = evoke_window(window_title=vpn_title)
                if vpn_control_on:
                    vpn_was_on = is_vpn_on(image_path=button_image_path)
                    button_position = None
                    if vpn_was_on:
                        button_position = turn_off_vpn(image_path=button_image_path)
                    minimize_window(window_title=vpn_title)
                else:
                    logger.info("Skipping VPN control process.")

                result = None
                try:
                    result = func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Unable to execute the decorated function: {e}")

                if vpn_control_on and vpn_was_on:
                    evoke_window(window_title=vpn_title)
                    turn_on_vpn(button_position)
                    minimize_window(window_title=vpn_title)
                return result
            return wrapper
        return decorator
    return manage_vpn_state

manage_vpn_state = create_manage_vpn_state_decorator(manage_vpn_on=True)
@manage_vpn_state(vpn_title="LetsVPN", button_image_path="assets/turnoffvpn.png")
def wrapped_function():
    logger.info("Executing the wrapped function...")
    time.sleep(2)
    logger.info("Wrapped function execution complete.")

def main():
    wrapped_function()

if __name__ == "__main__":
    main()
