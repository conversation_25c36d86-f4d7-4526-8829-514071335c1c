# -*- coding: utf-8 -*-
import os
import cv2
import base64
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from PIL import Image, ImageChops
from io import BytesIO
import numpy as np
from typing import Tuple, Dict, List
import logging
import time
from selenium.common.exceptions import TimeoutException, ElementClickInterceptedException
from random import random
from cjy import Cjy_client

from page_func import wait_for_loading
from decorators import exception_handler, retry
from logger_setup import get_logger

@exception_handler('刷新验证码', False)
def refresh_captcha(driver: WebDriver) -> bool:
    refresh_selector = "#mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div:nth-child(5) > div.mask > div > div.verifybox-bottom > div > div.verify-img-out > div > div"
    try:
        refresh_element = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, refresh_selector)))
        refresh_element.click()
    except ElementClickInterceptedException:
        cancel_selector = "body > div:nth-child(30) > div.ivu-modal-wrap > div > div > div > div > div.ivu-modal-confirm-footer > button"
        cancel_element = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, cancel_selector)))
        cancel_element.click()
        return False
    wait_for_loading(driver)
    return True


@exception_handler('获取图片', tuple())
def get_image(driver: WebDriver) -> tuple[Image.Image, dict[str, int], tuple[str, str, str]]:
    '''
    从网页获取图片对象、图片位置和长宽、要点击的汉字
    '''
    logger = get_logger()
    image_selector = "#mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div:nth-child(5) > div.mask > div > div.verifybox-bottom > div > div.verify-img-out > div > img"
    image_element = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, image_selector)))

    # 获取图片位置和宽高. keys: x, y, width, height
    image_position: dict[str, int] = image_element.location | image_element.size
    logger.debug(f'image_position: {image_position}')

    image_url = image_element.get_attribute('src')
    if image_url is None:
        raise ValueError("Image src attribute is None.")
    header, encoded = image_url.split(',', 1)
    image_data = base64.b64decode(encoded)
    image = Image.open(BytesIO(image_data))
    # image.save('image.png')


    charactors_selector = "#mobilePage > div > div > div.reservationStep1 > div:nth-child(2) > div:nth-child(5) > div.mask > div > div.verifybox-bottom > div > div.verify-bar-area > span"
    character_element = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CSS_SELECTOR, charactors_selector)))
    characters_list = character_element.text.split('【')[1].split('】')[0].split(',')
    if len(characters_list) != 3:
        raise ValueError(f"Expected 3 characters, got {len(characters_list)}: {characters_list}")
    characters: tuple[str, str, str] = tuple(characters_list)  # type: ignore
    logger.debug(f'characters: {characters}')

    return image, image_position, characters


def download_captcha_images(driver: WebDriver, save_dir: str = "assets/original_pc") -> None:
    """
    Downloads captcha images until the webpage forbids further refreshing.

    :param driver: Selenium WebDriver instance.
    :param save_dir: Directory to save the images.
    """
    logger = get_logger()
    os.makedirs(save_dir, exist_ok=True)

    existing_files = [f for f in os.listdir(save_dir) if f.endswith(".png")]
    if existing_files:
        existing_indices = [int(f.split(".")[0]) for f in existing_files if f.split(".")[0].isdigit()]
        count = max(existing_indices, default=-1) + 1
    else:
        count = 0

    last_characters = None
    attempt = 0
    while 1:
        image, _, characters = get_image(driver)
        if attempt < 3:
            if last_characters and characters == last_characters:
                logger.info(f"New image not loaded×{attempt + 1}. waiting...")
                time.sleep(1)
                attempt += 1
                continue
            else:
                image_path = os.path.join(save_dir, f"{count}.png")
                image.save(image_path)
                logger.info(f"Saved captcha image: {image_path}")
                last_characters = characters
                count += 1
                attempt = 0

        # Refresh the captcha. Introduce a small random delay to avoid being flagged by the server
        if refresh_captcha(driver):
            time.sleep(1.5 + 0.5*random())
            continue
        else:
            break

    logger.info(f"Downloaded {count} captcha images.")


def subtract_background(cv_img: np.ndarray, background_dir: str = "assets/background") -> np.ndarray:
    """
    Finds and subtracts the closest matching background from the captcha image.

    :param cv_img: The captcha image as a numpy array.
    :param background_dir: Directory containing the background images.
    :return: Captcha image with background removed.
    """
    logger = get_logger()
    input_image = Image.fromarray(cv_img)

    backgrounds = [f for f in os.listdir(background_dir) if f.endswith(".png")]
    min_diff = 100
    best_match = None
    best_match_index = None

    for i, bg_file in enumerate(backgrounds):
        bg_path = os.path.join(background_dir, bg_file)
        bg_image = Image.open(bg_path)
        diff = ImageChops.difference(input_image, bg_image)
        diff_array = np.array(diff)
        mean_diff = np.mean(diff_array)

        if mean_diff < min_diff:
            min_diff = mean_diff
            best_match = bg_image
            best_match_index = i
            if min_diff < 6.3:
                break

    if best_match is not None:
        if min_diff < 6.3:
            logger.debug(f"Matched background {best_match_index} with difference: {min_diff:.2f}")
        else:
            logger.warning(f"No matched background. Using best match {best_match_index} with difference {min_diff:.2f}")
        diff_image = ImageChops.difference(input_image, best_match)
        return cv2.cvtColor(np.array(diff_image), cv2.COLOR_RGB2BGR)

    return cv_img


def preprocess_image(cv_img: np.ndarray) -> np.ndarray:
    """
    Preprocess the captcha image for OCR by removing the background.

    :param cv_img: The captcha image as a numpy array.
    :return: Preprocessed image ready for OCR.
    """
    cv_img = cv2.pyrMeanShiftFiltering(cv_img, sp=10, sr=20)
    gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
    _, bin_img = cv2.threshold(gray, 5, 255, cv2.THRESH_BINARY)
    bin_img = cv2.bitwise_not(bin_img)
    bin_img = bin_img.astype(np.uint8)
    bin_img = cv2.erode(bin_img, np.ones((2, 2), np.uint8), iterations=1)
    bin_img = cv2.dilate(bin_img, np.ones((2, 2), np.uint8), iterations=1)

    return bin_img


@exception_handler("点击验证码字符", False)
@retry(3, "点击验证码字符")
def click_characters_in_image(
    driver: WebDriver,
    image: Image.Image,
    image_position: Dict[str, int],
    characters: tuple[str, str, str],
    cjy_user_name: str,
    cjy_password: str,
    cjy_soft_id: str,
) -> bool:
    logger = get_logger()
    # os.makedirs('assets/temp_images', exist_ok=True)
    
    cv_img = np.array(image.convert('RGB'))
    cv_img = subtract_background(cv_img)
    cv_bgr = cv2.cvtColor(cv_img, cv2.COLOR_RGB2BGR)
    # cv2.imwrite('assets/temp_images/subtracted.png', cv_bgr)
    bin_img = preprocess_image(cv_bgr)
    # cv2.imwrite('assets/temp_images/binarized.png', bin_img)

    cjy_client = Cjy_client(cjy_user_name, cjy_password, cjy_soft_id)
    captcha_result = cjy_client.PostPic_base64(base64.b64encode(cv2.imencode('.png', bin_img)[1]).decode(), 9501)
    if captcha_result['err_no'] != 0:
        logger.error(f"Failed to recognize characters. Error {captcha_result['err_no']}: {captcha_result['err_str']}")
        return False
    all_boxes = {ch: (int(x), int(y)) for ch, x, y in map(lambda x: x.split(','), captcha_result['pic_str'].split('|'))}
    logger.debug(f"Recognized characters: {all_boxes}")

    scroll_x_offset = driver.execute_script("return window.pageXOffset;")
    scroll_y_offset = driver.execute_script("return window.pageYOffset;")
    logger.debug(f"Scroll offset: {scroll_x_offset}, {scroll_y_offset}")

    click_positions = []
    for ch in characters:
        if ch not in all_boxes:
            logger.warning(f"Character '{ch}' not matched, refreshing...")
            refresh_captcha(driver)
            raise RuntimeError("Character not matched")
        else:
            click_positions.append(all_boxes[ch])

    for position in click_positions:
        image_x, image_y = image_position['x'], image_position['y']
        character_x, character_y = position
        cx = image_x + character_x - scroll_x_offset
        cy = image_y + character_y - scroll_y_offset
        logger.debug(f"Clicking character at ({cx}, {cy})")
        ActionChains(driver).move_by_offset(cx, cy).click().perform()
        ActionChains(driver).reset_actions()

    time.sleep(0.5)
    return True
