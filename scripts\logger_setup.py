# -*- coding: utf-8 -*-
import logging
import threading

_logger = None
_logger_lock = threading.Lock()

def setup_logger(config_file: str) -> logging.Logger:
    """
    Configure and return a logger based on the provided config_file.
    This function should be called once per process.
    """
    global _logger
    with _logger_lock:
        if _logger is not None:
            # Logger is already set up
            return _logger

        log_file = f'{config_file.split(".")[0]}.log'
        
        # Create a logger with the config_file name
        logger = logging.getLogger(config_file)
        logger.setLevel(logging.DEBUG)
        logger.propagate = False  # Prevent log messages from being propagated to the root logger

        # Create handlers
        fh = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        fh.setLevel(logging.DEBUG)
        
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)
        
        # Create formatter and add it to handlers
        formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        fh.setFormatter(formatter)
        ch.setFormatter(formatter)
        
        # Add handlers to the logger
        logger.addHandler(fh)
        logger.addHandler(ch)
        
        # Set external library log levels to WARNING
        for lib in ['selenium', 'WDM', 'urllib3', 'PIL']:
            logging.getLogger(lib).setLevel(logging.WARNING)
        
        _logger = logger
        return _logger

def get_logger() -> logging.Logger:
    """
    Retrieve the configured logger.
    Raises an exception if the logger has not been set up yet.
    """
    if _logger is None:
        raise RuntimeError("Logger has not been initialized. Call setup_logger() first.")
    return _logger
