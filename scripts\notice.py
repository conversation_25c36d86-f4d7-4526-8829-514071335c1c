# -*- coding: utf-8 -*-
from urllib.parse import quote
from urllib import request
import json
from configparser import ConfigParser
from typing import List, Tuple
import datetime
from decorators import exception_handler
import logger_setup

@exception_handler('微信通知', False)
def wechat_notification(user_name: str, venue_type: str, booked_venues: List[Tuple[datetime.date, int, str]], sckey: str, message: str = '', test = False):
    booked_info_list = []
    for date, venue_num, time in booked_venues:
        time = time[:2] + ':' + time[2:4] + '~' + time[5:7] + ':' + time[7:]
        booked_info = f'{time} {venue_num}号场'
        booked_info_list.append(booked_info)
    booked_info_str = str(date) + ' ' + '；'.join(booked_info_list)
    if not message:
        message = 'Test' if test else '预约成功'
        desp = f'{user_name} 成功预约{venue_type}：{booked_info_str}'
    elif message == '请手动完成支付':
        desp = f'{user_name} 成功预约{venue_type}：{booked_info_str}，但需要手动完成支付'
    else:
        desp = f'{user_name}预约{venue_type} {booked_info_str}时遇到了{message}'
    url = f'https://sctapi.ftqq.com/{sckey}.send?title={message}&desp={desp}'
    with request.urlopen(quote(url, safe='/:?=&')) as response:
        response = json.loads(response.read().decode('utf-8'))
        if response['code'] == 0 and response['data']['error'] == 'SUCCESS':
            return True
        else:
            raise RuntimeError(f'{response["errno"]} error: {response["errmsg"]}')


if __name__ == '__main__':
    logger = logger_setup.setup_logger('notice_test.ini')
    conf = ConfigParser()
    conf.read('config0.ini', encoding='utf8')
    user_name = conf['login']['user_name']
    venue_type = conf['type']['venue_type']
    sckey = conf['notice']['SCKEY']
    test_booked_venues = [(datetime.date.today(), 1, '1900-2000'), (datetime.date.today(), 2, '2000-2100')]
    result = wechat_notification(user_name, venue_type, test_booked_venues, sckey, test=True)
    print('Notification sent successfully!' if result else 'Notification failed.')
